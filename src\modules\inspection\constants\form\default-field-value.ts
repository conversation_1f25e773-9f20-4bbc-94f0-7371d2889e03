import { v4 as uuidv4 } from "uuid";
import { ICreateFieldForm } from "../../validators/form/create-field";

export const CREATE_DEFAULT_FIELD = (sequence: number): ICreateFieldForm => {
	return {
		tempId: uuidv4(),
		field: { id: undefined, name: "" },
		groupTitle: "",
		nickname: "",
		required: false,
		group: undefined,
		typeId: undefined,
		measure: { id: undefined, name: "" },
		biFilter: false,
		sequence: sequence,
	};
};
