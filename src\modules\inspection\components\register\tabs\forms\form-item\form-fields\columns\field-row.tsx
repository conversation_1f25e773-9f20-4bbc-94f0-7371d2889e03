import { updateFieldData<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/field-values.atom";
import useFindAllFields from "@/modules/inspection/hooks/fields/list/find-all.hook";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { GenericSearchSelect } from "@/shared/components/custom/generic-search-select";
import { Row } from "@tanstack/react-table";
import { useSetAtom } from "jotai";

export const InspectionFormFieldRow = ({ row }: { row: Row<ICreateFieldForm> }) => {
	const { tempId, field } = row.original;
	const updateField = useSetAtom(updateFieldDataAtom);

	return (
		<GenericSearchSelect
			value={{ id: field.id ?? 0, name: field.name }}
			onChange={selected => updateField({ tempId, field: selected })}
			useDataHook={useFindAllFields}
			placeholder="Selecione..."
			searchPlaceholder="Buscar campo..."
			loadingText="Carregando campos..."
			emptyText="Nenhum campo encontrado."
			width="w-full"
		/>
	);
};
