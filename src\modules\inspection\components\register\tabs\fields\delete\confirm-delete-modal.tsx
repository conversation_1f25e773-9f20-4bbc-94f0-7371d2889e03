import { useDeleteFieldsMutation } from "@/modules/inspection/hooks/fields/delete/delete-fields-mutation.hook";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/shadcn/button";
import { Trash } from "lucide-react";

export interface IConfirmDeleteFieldsModalProps {
	fieldsId: string;
	isOpen: boolean;
	name?: string;
	onClose: () => void;
}

export function ConfirmDeleteFieldsModal({ fieldsId, isOpen, name, onClose }: IConfirmDeleteFieldsModalProps) {
	const { deleteFields } = useDeleteFieldsMutation();
	function handleConfirm() {
		deleteFields(fieldsId);
		onClose();
	}
	return (
		<Modal showCloseButton={false} isOpen={isOpen} onClose={onClose}>
			<div className="flex flex-col items-center space-y-4 p-2 text-center">
				<Trash className="h-12 w-12 text-red-500" />

				<div className="space-y-2">
					<h2 className="text-xl font-semibold">Deletar campo</h2>
					<p className="text-muted-foreground">
						Você realmente deseja apagar o campo? <span className="text-primary font-medium">{name}</span>
					</p>
				</div>

				<div className="flex gap-3 pt-2">
					<Button variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button className="bg-red-400" onClick={handleConfirm}>
						Confirmar
					</Button>
				</div>
			</div>
		</Modal>
	);
}
