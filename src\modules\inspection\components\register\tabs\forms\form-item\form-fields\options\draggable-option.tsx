import { removeField<PERSON>ption<PERSON>tom, updateFieldOption<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/field-values.atom";
import { Button } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useSetAtom } from "jotai";
import { GripVertical, X } from "lucide-react";
import { forwardRef, useState } from "react";

interface FieldOption {
	sequence?: number;
	option: string;
	tempId: string;
	id?: number;
}

interface DraggableOptionProps {
	option: FieldOption;
	fieldTempId: string;
	isLast?: boolean;
}

export const DraggableOption = forwardRef<HTMLInputElement, DraggableOptionProps>(({ option, fieldTempId, isLast }, ref) => {
	const [localValue, setLocalValue] = useState(option.option);
	const updateFieldOption = useSetAtom(updateFieldOptionAtom);
	const removeFieldOption = useSetAtom(removeFieldOptionAtom);

	const { setNodeRef, transform, transition, isDragging, attributes, listeners } = useSortable({
		id: option.tempId,
	});

	const handleInputChange = (value: string) => {
		setLocalValue(value);
	};

	const handleInputBlur = () => {
		if (localValue !== option.option) {
			updateFieldOption({
				tempId: fieldTempId,
				optionTempId: option.tempId,
				updatedOption: {
					...option,
					option: localValue,
				},
			});
		}
	};

	const handleRemove = () => {
		removeFieldOption({
			tempId: fieldTempId,
			optionTempId: option.tempId,
		});
	};

	return (
		<div
			ref={setNodeRef}
			style={{
				transform: CSS.Transform.toString(transform),
				transition,
				opacity: isDragging ? 0.7 : 1,
			}}
			className="group flex items-center gap-2"
		>
			<div
				{...attributes}
				{...listeners}
				className="flex cursor-grab items-center justify-center opacity-50 transition-opacity hover:opacity-100 active:cursor-grabbing"
			>
				<GripVertical className="text-muted-foreground h-4 w-4" />
			</div>

			<div className="flex-1">
				<Input
					ref={isLast ? ref : undefined}
					value={localValue}
					onChange={e => handleInputChange(e.target.value)}
					onBlur={handleInputBlur}
					placeholder="Digite uma opção"
					className="w-full bg-white"
				/>
			</div>

			<Button
				type="button"
				variant="ghost"
				size="sm"
				onClick={handleRemove}
				className="hover:bg-destructive/10 hover:text-destructive h-8 w-8 p-0 opacity-0 transition-opacity group-hover:opacity-100"
				title="Remover opção"
			>
				<X className="h-4 w-4" />
			</Button>
		</div>
	);
});

DraggableOption.displayName = "DraggableOption";
