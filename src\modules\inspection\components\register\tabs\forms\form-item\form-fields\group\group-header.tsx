import { updateFieldGroupTitleAtom } from "@/modules/inspection/atoms/forms/fields/group-actions.atom";
import { IFieldGroup } from "@/modules/inspection/types/forms/fields-table/fields-group.type";
import { Button } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { DraggableAttributes } from "@dnd-kit/core";
import { SyntheticListenerMap } from "@dnd-kit/core/dist/hooks/utilities";
import { useSetAtom } from "jotai";
import { GripVertical, Plus, Trash2 } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";

interface IGroupHeaderProps {
	group: IFieldGroup;
	onAddItem?: () => void;
	onRemoveGroup?: () => void;
	dragProps?: {
		attributes?: DraggableAttributes;
		listeners?: SyntheticListenerMap | undefined;
	};
	isOverlay?: boolean;
	autoFocus?: boolean;
}

export const GroupHeader: React.FC<IGroupHeaderProps> = ({ group, onAddItem, onRemoveGroup, dragProps, isOverlay = false, autoFocus = false }) => {
	const [title, setTitle] = useState(group.groupTitle);
	const inputRef = useRef<HTMLInputElement>(null);

	const updateTitle = useSetAtom(updateFieldGroupTitleAtom);

	useEffect(() => {
		setTitle(group.groupTitle);
	}, [group.groupTitle]);

	useEffect(() => {
		if (autoFocus && inputRef.current) {
			inputRef.current.focus();
		}
	}, [autoFocus]);

	const debouncedUpdateTitle = useDebounce(
		(...args: unknown[]) => {
			const newTitle = args[0] as string;
			updateTitle({ groupId: group.tempId, newTitle });
		},
		{ delay: 500 },
	);

	const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setTitle(e.target.value);
		debouncedUpdateTitle(e.target.value);
	};

	return (
		<div
			className={`from-primary/20 to-primary/30 flex w-full items-center justify-between border-b bg-gradient-to-r shadow-sm`}
			data-group-header="true"
			data-group-id={group.tempId}
		>
			<div className="flex flex-1 items-center gap-2">
				<div className="bg-primary/10 rounded-main ml-1 flex h-full w-fit items-center p-2">
					<Button
						{...(dragProps?.attributes || {})}
						{...(dragProps?.listeners || {})}
						variant="ghost"
						size="icon"
						className="text-primary hover:bg-primary/20 size-7 cursor-grab transition-colors active:cursor-grabbing"
					>
						<GripVertical className="size-4" />
						<span className="sr-only">Arraste para reordenar</span>
					</Button>
				</div>
				<div className="flex-1 p-2 font-semibold">
					<Input
						ref={inputRef}
						value={title}
						onChange={handleTitleChange}
						className="border-primary/30 focus-visible:ring-primary/40 h-9 bg-white/70 font-medium"
						placeholder="Título do grupo"
					/>
				</div>
			</div>
			{!isOverlay && (
				<div className="flex gap-2 px-3 py-1.5">
					<Button
						size="sm"
						variant="outline"
						onClick={e => {
							e.preventDefault();
							onAddItem?.();
						}}
						className="text-primary hover:bg-primary/10 hover:text-primary-dark border-primary/30 rounded-controls flex items-center gap-2 bg-white px-3 py-2 transition"
						title="Adicionar campo ao grupo"
					>
						<Plus className="size-4" />
						<span className="font-medium">Adicionar</span>
					</Button>
					<Button
						size="sm"
						onClick={e => {
							e.preventDefault();
							onRemoveGroup?.();
						}}
						className="rounded-controls h-9 w-9 bg-white/80 p-0 text-red-600 shadow-sm transition-all duration-200 hover:bg-red-100 hover:text-red-800 hover:shadow-md"
						title="Remover grupo"
					>
						<Trash2 className="size-4" />
					</Button>
				</div>
			)}
		</div>
	);
};
