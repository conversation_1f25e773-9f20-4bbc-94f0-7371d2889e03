import { updateField<PERSON>ickname<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/field-values.atom";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Input } from "@/shared/components/shadcn/input";
import { Row } from "@tanstack/react-table";
import { useSet<PERSON>tom } from "jotai";

export const InspectionFormNicknameRow = ({ row }: { row: Row<ICreateFieldForm> }) => {
	const { tempId, nickname } = row.original;
	const updateField = useSetAtom(updateFieldNicknameAtom);

	return <Input placeholder="Apelido" className="w-full bg-white" value={nickname} onChange={e => updateField({ tempId, nickname: e.target.value })} />;
};
