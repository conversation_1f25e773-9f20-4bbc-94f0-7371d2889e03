export interface IUpdateFormDTO {
	title: string;
	text?: string;
	nomenclature: string;
	developerId: number;
	approverId: number;
	fields: IFieldForm[];
}

export interface IFieldForm {
	fieldId: number;
	nickname: string;
	required: boolean;
	group: number;
	sequence: number;
	typeId: number;
	measureId: number;
	groupTitle: string;
	biFilter: boolean;
	id?: number;
	options: IOption[];
}

interface IOption {
	sequence: number;
	option: string;
	id?: number;
}
