import z from "zod";

export const createFormSchema = z.object({
	title: z.string().min(1, "O título é obrigatório"),
	text: z.string().optional(),
	nomenclature: z.string().min(1, "A nomenclatura é obrigatória"),
	// approver: z.object({
	// 	id: z.string().min(1, "O ID do aprovador é obrigatório"),
	// 	name: z.string().min(1, "O nome do aprovador é obrigatório"),
	// }),
	// developer: z.object({
	// 	id: z.string().min(1, "O ID do elaborador é obrigatório"),
	// 	name: z.string().min(1, "O nome do elaborador é obrigatório"),
	// }),
	developer: z
		.object({
			id: z.string().optional(),
			name: z.string().optional(),
		})
		.optional(),
	approver: z
		.object({
			id: z.string().optional(),
			name: z.string().optional(),
		})
		.optional(),
});

export type ICreateForm = z.infer<typeof createFormSchema>;
