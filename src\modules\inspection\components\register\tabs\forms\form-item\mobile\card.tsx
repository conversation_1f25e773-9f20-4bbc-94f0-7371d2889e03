import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";

interface MobileFieldCardProps {
	field: ICreateFieldForm;
	index: number;
	onDelete: (index: number) => void;
	onMoveUp: (index: number) => void;
	onMoveDown: (index: number) => void;
	onCardClick?: (tempId: string) => void;
	isSelected: boolean;
	isFirst: boolean;
	isLast: boolean;
	isMoving?: boolean;
}

export const MobileFieldCard: React.FC<MobileFieldCardProps> = ({
	field,
	index,
	control,
	onDelete,
	onMoveUp,
	onMoveDown,
	onCardClick,
	isSelected,
	isFirst,
	isLast,
	isMoving,
}) => {
	return (
		<div className={`mobile-field-card ${isSelected ? "selected" : ""}`}>
			<h3>{field.field.id}</h3>
			<div className="actions">
				<button onClick={() => onMoveUp(index)} disabled={isFirst}>
					Move Up
				</button>
				<button onClick={() => onMoveDown(index)} disabled={isLast}>
					Move Down
				</button>
				<button onClick={() => onDelete(index)}>Delete</button>
			</div>
		</div>
	);
};
