import { CREATE_DEFAULT_FIELD } from "@/modules/inspection/constants/form/default-field-value";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { arrayMove } from "@dnd-kit/sortable";
import { atom } from "jotai";
import { getTotalItemsCountAtom } from "./group-actions.atom";
import { fieldsGroupsAtom } from "./group.atom";

export const addItemToGroupAtom = atom(null, (get, set, groupId: string) => {
	const groups = get(fieldsGroupsAtom);
	const totalItemsCount = get(getTotalItemsCountAtom);
	const newItem = CREATE_DEFAULT_FIELD(totalItemsCount + 1);

	set(
		fieldsGroupsAtom,
		groups.map(group => (group.tempId === groupId ? { ...group, items: [...group.items, newItem] } : group)),
	);
});

export const removeItemFromGroupAtom = atom(null, (get, set, { itemId }: { itemId: string }) => {
	const groups = get(fieldsGroupsAtom);

	set(
		fieldsGroupsAtom,
		groups.map(group => ({
			...group,
			items: group.items.filter(item => item.tempId !== itemId),
		})),
	);
});

// export const updateItemInGroupAtom = atom(null, (get, set, { groupId, updatedItem }: { groupId: string; updatedItem: ICreateFieldForm }) => {
// 	const groups = get(fieldsGroupsAtom);

// 	set(
// 		fieldsGroupsAtom,
// 		groups.map(group =>
// 			group.tempId === groupId
// 				? {
// 						...group,
// 						items: group.items.map(item => (item.tempId === updatedItem.tempId ? updatedItem : item)),
// 					}
// 				: group,
// 		),
// 	);
// });

export const reorderItemsInGroupAtom = atom(null, (get, set, { groupId, fromIndex, toIndex }: { groupId: string; fromIndex: number; toIndex: number }) => {
	const groups = get(fieldsGroupsAtom);

	const updatedGroups = groups.map(group => {
		if (group.tempId === groupId) {
			return { ...group, items: arrayMove(group.items, fromIndex, toIndex) };
		}
		return group;
	});

	let sequenceCounter = 1;
	const groupsWithUpdatedSequence = updatedGroups.map(group => {
		const updatedItems = group.items.map(item => ({
			...item,
			sequence: sequenceCounter++,
		}));
		return { ...group, items: updatedItems };
	});

	set(fieldsGroupsAtom, groupsWithUpdatedSequence);
});

export const duplicateItemInGroupAtom = atom(null, (get, set, { groupId, itemId }: { groupId: string; itemId: string }) => {
	const groups = get(fieldsGroupsAtom);
	const totalItemsCount = get(getTotalItemsCountAtom);

	set(
		fieldsGroupsAtom,
		groups.map(group => {
			if (group.tempId === groupId) {
				const itemToDuplicate = group.items.find(item => item.tempId === itemId);
				if (itemToDuplicate) {
					const duplicatedItem = {
						...itemToDuplicate,
						tempId: CREATE_DEFAULT_FIELD(totalItemsCount + 1).tempId,
						sequence: totalItemsCount + 1,
						nickname: `${itemToDuplicate.nickname} (cópia)`,
					};
					return { ...group, items: [...group.items, duplicatedItem] };
				}
			}
			return group;
		}),
	);
});

export const moveItemBetweenGroupsAtom = atom(
	null,
	(
		get,
		set,
		{
			fromGroupId,
			toGroupId,
			itemId,
		}: {
			fromGroupId: string;
			toGroupId: string;
			itemId: string;
		},
	) => {
		const groups = get(fieldsGroupsAtom);
		let itemToMove: ICreateFieldForm | null = null;

		const updatedGroups = groups.map(group => {
			if (group.tempId === fromGroupId) {
				itemToMove = group.items.find(item => item.tempId === itemId) || null;
				return { ...group, items: group.items.filter(item => item.tempId !== itemId) };
			}
			return group;
		});

		if (itemToMove) {
			const finalGroups = updatedGroups.map(group => (group.tempId === toGroupId ? { ...group, items: [...group.items, itemToMove!] } : group));

			set(fieldsGroupsAtom, finalGroups);
		}
	},
);

export const findItemByIdAtom = atom(null, (get, set, itemId: string) => {
	const groups = get(fieldsGroupsAtom);

	for (const group of groups) {
		const item = group.items.find(item => item.tempId === itemId);
		if (item) {
			return { item, groupId: group.tempId };
		}
	}

	return null;
});

export const getGroupItemsCountAtom = atom(null, (get, set, groupId: string) => {
	const groups = get(fieldsGroupsAtom);
	const group = groups.find(group => group.tempId === groupId);
	return group ? group.items.length : 0;
});

export const clearGroupItemsAtom = atom(null, (get, set, groupId: string) => {
	const groups = get(fieldsGroupsAtom);

	set(
		fieldsGroupsAtom,
		groups.map(group => (group.tempId === groupId ? { ...group, items: [] } : group)),
	);
});

export const updateGroupItemsSequenceAtom = atom(null, (get, set, groupId: string) => {
	const groups = get(fieldsGroupsAtom);

	set(
		fieldsGroupsAtom,
		groups.map(group =>
			group.tempId === groupId
				? {
						...group,
						items: group.items.map((item, index) => ({
							...item,
							sequence: index + 1,
						})),
					}
				: group,
		),
	);
});
