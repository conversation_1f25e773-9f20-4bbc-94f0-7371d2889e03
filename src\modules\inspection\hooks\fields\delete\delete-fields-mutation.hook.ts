import { toast } from "@/core/toast";
import { FIELDS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createDeleteRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useDeleteFieldsMutation() {
	const queryClient = useQueryClient();
	const deleteFields = useMutation({
		mutationKey: ["delete-fields"],
		mutationFn: async (id: string) => {
			const res = await createDeleteRequest<ApiResponse<IMessageGlobalReturn>>(FIELDS_ENDPOINTS.DELETE(id));
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["fields"],
				exact: false,
			});
		},
	});

	return {
		deleteFields: (id: string) =>
			toast.promise(deleteFields.mutateAsync(id), {
				loading: "Excluindo campo...",
				success: "Campo excluído com sucesso!",
				error: "Erro ao excluir campo.",
			}),
	};
}
