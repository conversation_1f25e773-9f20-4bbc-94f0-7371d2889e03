// import { arrayMove } from "@dnd-kit/sortable";
// import { atom } from "jotai";
// import { InspectionFormTypeEnum } from "../../constants/form/type-enum";
// import { ICreateFieldForm } from "../../validators/form/create-field";

// export interface IGroupFields {
// 	group: number | undefined;
// 	groupTitle: string | undefined;
// 	fields: ICreateFieldForm[];
// 	groupTempId: string;
// }

// export const fieldsFormAtom = atom<IGroupFields[]>([]);

// export const addGroupAtom = atom(null, (get, set, newGroup: IGroupFields) => {
// 	const fields = get(fieldsFormAtom);
// 	set(fieldsFormAtom, [...fields, newGroup]);
// });

// export const reorderGroupsAtom = atom(null, (get, set, { activeIndex, overIndex }: { activeIndex: number; overIndex: number }) => {
// 	const fields = get(fieldsForm<PERSON>tom);
// 	const updatedFields = arrayMove(fields, activeIndex, overIndex);
// 	set(fieldsFormAtom, updatedFields);
// });

// export const selectedFieldAtom = atom<string | null>(null);

// export const fieldSelectAtom = atom(null, (get, set, tempId: string | null) => {
// 	set(selectedFieldAtom, tempId);
// });

// export const selectedFieldDataAtom = atom(get => {
// 	const selectedTempId = get(selectedFieldAtom);
// 	const groupFields = get(fieldsFormAtom);
// 	if (!selectedTempId) return null;

// 	for (const group of groupFields) {
// 		const field = group.fields.find(field => field.tempId === selectedTempId);
// 		if (field) return field;
// 	}
// 	return null;
// });

// export const addFieldAtom = atom(null, (get, set, newField: ICreateFieldForm, currentGroup: number | undefined) => {
// 	const fields = get(fieldsFormAtom);
// 	const group = fields.find(group => group.group === currentGroup);
// 	if (group) {
// 		group.fields.push(newField);
// 		set(fieldsFormAtom, [...fields]);
// 	}
// });

// export const removeFieldAtom = atom(null, (get, set, fieldToRemove: ICreateFieldForm) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.filter(field => field.tempId !== fieldToRemove.tempId),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldAtom = atom(null, (get, set, updatedField: ICreateFieldForm) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(field => (field.tempId === updatedField.tempId ? updatedField : field)),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldSequenceAtom = atom(null, (get, set, { tempId, newSequence }: { tempId: string; newSequence: number }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(field => (field.tempId === tempId ? { ...field, sequence: newSequence } : field)),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldNicknameAtom = atom(null, (get, set, { tempId, nickname }: { tempId: string; nickname: string }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(field => (field.tempId === tempId ? { ...field, nickname } : field)),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldRequiredAtom = atom(null, (get, set, { tempId, required }: { tempId: string; required: boolean }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(field => (field.tempId === tempId ? { ...field, required } : field)),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldGroupAtom = atom(null, (get, set, { tempId, group }: { tempId: string; group: number | undefined }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(groupItem => ({
// 		...groupItem,
// 		fields: groupItem.fields.map(field => (field.tempId === tempId ? { ...field, group } : field)),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldGroupTitleAtom = atom(null, (get, set, { tempId, groupTitle }: { tempId: string; groupTitle: string }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(field => (field.tempId === tempId ? { ...field, groupTitle } : field)),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldTypeAtom = atom(null, (get, set, { tempId, typeId }: { tempId: string; typeId: InspectionFormTypeEnum }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(field => (field.tempId === tempId ? { ...field, typeId } : field)),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldMeasureAtom = atom(null, (get, set, { tempId, measure }: { tempId: string; measure: { id: number; name: string } }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(field => (field.tempId === tempId ? { ...field, measure } : field)),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldDataAtom = atom(null, (get, set, { tempId, field }: { tempId: string; field: { id?: number; name: string } }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(fieldItem => (fieldItem.tempId === tempId ? { ...fieldItem, field } : fieldItem)),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldBiFilterAtom = atom(null, (get, set, { tempId, biFilter }: { tempId: string; biFilter: boolean }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(field => (field.tempId === tempId ? { ...field, biFilter } : field)),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldOptionsAtom = atom(
// 	null,
// 	(get, set, { tempId, options }: { tempId: string; options: Array<{ sequence?: number; option: string; tempId: string; id?: number }> }) => {
// 		const groupFields = get(fieldsFormAtom);
// 		const updatedGroups = groupFields.map(group => ({
// 			...group,
// 			fields: group.fields.map(field => (field.tempId === tempId ? { ...field, options } : field)),
// 		}));
// 		set(fieldsFormAtom, updatedGroups);
// 	},
// );

// export const addFieldOptionAtom = atom(
// 	null,
// 	(get, set, { tempId, newOption }: { tempId: string; newOption: { sequence?: number; option: string; tempId: string; id?: number } }) => {
// 		const groupFields = get(fieldsFormAtom);
// 		const updatedGroups = groupFields.map(group => ({
// 			...group,
// 			fields: group.fields.map(field => {
// 				if (field.tempId === tempId) {
// 					const currentOptions = field.options || [];
// 					const nextSequence = currentOptions.length + 1;
// 					const optionWithSequence = { ...newOption, sequence: nextSequence };
// 					return { ...field, options: [...currentOptions, optionWithSequence] };
// 				}
// 				return field;
// 			}),
// 		}));
// 		set(fieldsFormAtom, updatedGroups);
// 	},
// );

// export const getFieldOptionsAtom = atom(get => {
// 	return (tempId: string) => {
// 		const groupFields = get(fieldsFormAtom);
// 		for (const group of groupFields) {
// 			const field = group.fields.find(field => field.tempId === tempId);
// 			if (field) return field.options || [];
// 		}
// 		return [];
// 	};
// });

// export const removeFieldOptionAtom = atom(null, (get, set, { tempId, optionTempId }: { tempId: string; optionTempId: string }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(field => {
// 			if (field.tempId === tempId && field.options) {
// 				const filteredOptions = field.options.filter(option => option.tempId !== optionTempId);
// 				return { ...field, options: filteredOptions };
// 			}
// 			return field;
// 		}),
// 	}));
// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateFieldOptionAtom = atom(
// 	null,
// 	(
// 		get,
// 		set,
// 		{
// 			tempId,
// 			optionTempId,
// 			updatedOption,
// 		}: { tempId: string; optionTempId: string; updatedOption: { sequence?: number; option: string; tempId: string; id?: number } },
// 	) => {
// 		const groupFields = get(fieldsFormAtom);
// 		const updatedGroups = groupFields.map(group => ({
// 			...group,
// 			fields: group.fields.map(field => {
// 				if (field.tempId === tempId && field.options) {
// 					const updatedOptions = field.options.map(option => (option.tempId === optionTempId ? updatedOption : option));
// 					return { ...field, options: updatedOptions };
// 				}
// 				return field;
// 			}),
// 		}));
// 		set(fieldsFormAtom, updatedGroups);
// 	},
// );

// export const reorderFieldOptionsAtom = atom(
// 	null,
// 	(get, set, { tempId, activeIndex, overIndex }: { tempId: string; activeIndex: number; overIndex: number }) => {
// 		const groupFields = get(fieldsFormAtom);
// 		const updatedGroups = groupFields.map(group => ({
// 			...group,
// 			fields: group.fields.map(field => {
// 				if (field.tempId === tempId && field.options) {
// 					const newOptions = [...field.options];
// 					const [movedItem] = newOptions.splice(activeIndex, 1);
// 					newOptions.splice(overIndex, 0, movedItem);

// 					const optionsWithUpdatedSequence = newOptions.map((option, index) => ({
// 						...option,
// 						sequence: index + 1,
// 					}));

// 					return { ...field, options: optionsWithUpdatedSequence };
// 				}
// 				return field;
// 			}),
// 		}));
// 		set(fieldsFormAtom, updatedGroups);
// 	},
// );

// export const reorderFieldsAtom = atom(null, (get, set, { activeIndex, overIndex }: { activeIndex: number; overIndex: number }) => {
// 	const groupFields = get(fieldsFormAtom);
// 	// Para reordenar campos, precisamos achatar todos os campos primeiro
// 	const allFields: ICreateFieldForm[] = [];
// 	groupFields.forEach(group => {
// 		allFields.push(...group.fields);
// 	});

// 	const newFields = [...allFields];
// 	const [movedItem] = newFields.splice(activeIndex, 1);
// 	newFields.splice(overIndex, 0, movedItem);

// 	const fieldsWithUpdatedSequence = newFields.map((field, index) => ({
// 		...field,
// 		sequence: index + 1,
// 	}));

// 	// Reorganizar de volta nos grupos
// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: fieldsWithUpdatedSequence.filter(field => field.group === group.group),
// 	}));

// 	set(fieldsFormAtom, updatedGroups);
// });

// export const updateAllFieldSequencesAtom = atom(null, (get, set) => {
// 	const groupFields = get(fieldsFormAtom);
// 	let sequenceCounter = 1;

// 	const updatedGroups = groupFields.map(group => ({
// 		...group,
// 		fields: group.fields.map(field => ({
// 			...field,
// 			sequence: sequenceCounter++,
// 		})),
// 	}));

// 	set(fieldsFormAtom, updatedGroups);
// });
