import { ICreateFormDTO } from "../../types/forms/dtos/create-form.dto";
import { IFieldGroup } from "../../types/forms/fields-table/fields-group.type";
import { ICreateForm } from "../../validators/form/create";

export class InspectionFormToCreateMapper {
	static map(data: ICreateForm, fields: IFieldGroup[]): ICreateFormDTO {
		return {
			title: data.title,
			text: data.text,
			nomenclature: data.nomenclature,
			// developerId: Number(data.developer.id),
			// approverId: Number(data.approver.id),
			developerId: 1, // [ ] Ajustar depois que ter rota de developer
			approverId: 1, // [ ] Ajustar depois que ter rota de approver
			fields: fields.flatMap(field =>
				field.items.map(item => ({
					fieldId: item.field.id!,
					nickname: item.nickname,
					required: item.required!,
					group: item.group!,
					sequence: item.sequence!,
					typeId: Number(item.typeId!),
					measureId: Number(item.measure.id!),
					groupTitle: item.groupTitle!,
					biFilter: item.biFilter!,
					options: (item.options ?? []).map((opt, optIdx) => ({
						sequence: opt.sequence ?? optIdx + 1,
						option: opt.option ?? "",
					})),
				})),
			),
		};
	}
}
