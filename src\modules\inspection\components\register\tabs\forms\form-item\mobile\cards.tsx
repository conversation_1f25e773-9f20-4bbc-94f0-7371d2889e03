import { addFieldGroup<PERSON>tom, generateEmptyGroupWithItems } from "@/modules/inspection/atoms/forms/fields/group-actions.atom";
import { fieldsGroupsAtom } from "@/modules/inspection/atoms/forms/fields/group.atom";
import { Button } from "@/shared/components/shadcn/button";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { Folder, Plus } from "lucide-react";
import { useState } from "react";
import { MobileFieldCard } from "./card";

export const MobileFieldsCards: React.FC = () => {
	const fieldsGroups = useAtomValue(fieldsGroupsAtom);
	const addField = useSetAtom(generateEmptyGroupWithItems);
	const addGroup = useSetAtom(addFieldGroupAtom);
	const [activeId, setActiveId] = useState<string | null>(null);

	const activeGroup = activeId ? fieldsGroups.find(g => g.tempId === activeId) : null;
	const lastGroupId = fieldsGroups.at(-1)?.tempId;

	return (
		<section className="mt-2 flex flex-col gap-4">
			<div className="flex w-full flex-wrap justify-end gap-2">
				<Button
					className="max-w-[200px] min-w-[140px] flex-1"
					onClick={e => {
						e.preventDefault();
						addGroup();
					}}
				>
					<Folder className="mr-2 size-4" />
					Adicionar grupo
				</Button>
				<Button
					className="max-w-[200px] min-w-[140px] flex-1"
					variant="outline"
					onClick={e => {
						e.preventDefault();
						addField();
					}}
				>
					<Plus className="mr-2 size-4" />
					Adicionar campo
				</Button>
			</div>

			<div className="rounded-main w-full border">
				{fieldsGroups.map(group =>
					group.items.map((field, idx) => (
						<MobileFieldCard
							key={field.tempId}
							field={field}
							index={idx}
							isFirst={idx === 0}
							isLast={idx === group.items.length - 1}
							isSelected={activeId === field.tempId}
							onDelete={() => {
								// Implement delete logic here
							}}
							onMoveUp={() => {
								// Implement move up logic here
							}}
							onMoveDown={() => {
								// Implement move down logic here
							}}
							onCardClick={() => setActiveId(field.tempId)}
						/>
					)),
				)}
			</div>
		</section>
	);
};
