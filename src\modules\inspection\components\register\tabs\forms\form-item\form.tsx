import { ICreateForm } from "@/modules/inspection/validators/form/create";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { Textarea } from "@/shared/components/shadcn/textarea";
import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { Check, Save, X } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { TableFields } from "./form-fields/table";
import { MobileFieldsCards } from "./mobile/cards";

interface IFormCreateFormProps {
	mode: "create" | "edit";
	onClose: () => void;
	methods: UseFormReturn<ICreateForm>;
	onSubmit: (data: ICreateForm) => void;
}

const developers = [
	{ id: "1", name: "<PERSON>" },
	{ id: "2", name: "<PERSON>" },
	{ id: "3", name: "Edi Mapepa" },
];

const approvers = [
	{ id: "1", name: "Kevin Luan Damm" },
	{ id: "2", name: "Leonardo Lobas Rockenbach" },
	{ id: "3", name: "Edi Mapepa" },
	{ id: "4", name: "Jheisinho Marls" },
];

const requiredLabel = (label: string) => (
	<>
		{label} <span className="text-red-500">*</span>
	</>
);

export const FormCreateForm: React.FC<IFormCreateFormProps> = ({ onClose, methods, onSubmit, mode }) => {
	const isMobile = useIsMobile();

	return (
		<Form {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)} className="flex flex-col gap-4">
				<FormField
					control={methods.control}
					name="title"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{requiredLabel("Título")}</FormLabel>
							<FormControl>
								<Input placeholder="Digite seu título" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={methods.control}
					name="text"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Descrição</FormLabel>
							<FormControl>
								<Textarea placeholder="Digite uma descrição para o formulário" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
					<FormField
						control={methods.control}
						name="nomenclature"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{requiredLabel("Nomenclatura")}</FormLabel>
								<FormControl>
									<Input placeholder="Digite a nomenclatura" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={methods.control}
						name="developer"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{requiredLabel("Elaborador")}</FormLabel>
								<Select
									onValueChange={val => {
										const selected = developers.find(dev => dev.id === val);
										field.onChange(selected ?? null);
									}}
									value={field.value?.id ?? ""}
								>
									<FormControl>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Selecione um elaborador" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{developers.map(dev => (
											<SelectItem key={dev.id} value={dev.id}>
												{dev.name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={methods.control}
						name="approver"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{requiredLabel("Aprovador")}</FormLabel>
								<Select
									onValueChange={val => {
										const selected = approvers.find(app => app.id === val);
										field.onChange(selected ?? null);
									}}
									value={field.value?.id ?? ""}
								>
									<FormControl>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Selecione um aprovador" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{approvers.map(app => (
											<SelectItem key={app.id} value={app.id}>
												{app.name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
				{isMobile ? <MobileFieldsCards /> : <TableFields />}
				<div className="flex justify-end gap-2 pt-4">
					<Button type="button" variant="outline" onClick={onClose}>
						<X className="mr-2" /> Cancelar
					</Button>
					<Button type="submit">
						{mode === "create" ? <Check className="mr-2" /> : <Save className="mr-2" />}
						{mode === "create" ? "Criar" : "Salvar"}
					</Button>
				</div>
			</form>
		</Form>
	);
};
