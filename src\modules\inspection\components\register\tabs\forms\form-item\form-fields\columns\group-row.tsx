// import { updateField<PERSON><PERSON><PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields-form.atom";
// import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
// import { Input } from "@/shared/components/shadcn/input";
// import { Row } from "@tanstack/react-table";
// import { useSetAtom } from "jotai";

// export const InspectionFormGroupRow = ({ row }: { row: Row<ICreateFieldForm> }) => {
// 	const { tempId, group } = row.original;
// 	const updateField = useSetAtom(updateFieldGroupAtom);

// 	return (
// 		<Input type="number" value={group ?? ""} onChange={e => updateField({ tempId, group: e.target.value === "" ? undefined : Number(e.target.value) })} />
// 	);
// };
