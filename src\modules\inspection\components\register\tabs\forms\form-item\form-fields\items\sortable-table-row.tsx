import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { flexRender, Row } from "@tanstack/react-table";
import { InspectionFieldOptions } from "../options/field-options";

interface SortableTableRowProps {
	row: Row<ICreateFieldForm>;
	isOverlay: boolean;
}

export const SortableTableRow = ({ row, isOverlay }: SortableTableRowProps) => {
	const { setNodeRef, transform, transition, isDragging } = useSortable({
		id: row.original.tempId,
		transition: {
			duration: 200,
			easing: "cubic-bezier(0.25, 1, 0.5, 1)",
		},
	});

	const isEmptyGroup = row.original.tempId.includes("empty") || false;
	const hasOptions = row.original.typeId === InspectionFormTypeEnum.OPTIONS && !isOverlay;

	const style = {
		transform: CSS.Transform.toString(transform),
		transition: isDragging ? "none" : transition,
		opacity: isDragging ? 0.5 : 1,
		zIndex: isDragging ? 999 : "auto",
	};

	return (
		<div ref={setNodeRef} style={style}>
			<TableRow key={row.id} className={`${isEmptyGroup ? "" : "bg-white/80 first:border-t-0 last:border-b-0"}`}>
				{row.getVisibleCells().map(cell => (
					<TableCell key={cell.id} style={{ width: cell.column.columnDef.meta?.width as string }}>
						<div className="flex w-full flex-col">{flexRender(cell.column.columnDef.cell, cell.getContext())}</div>
					</TableCell>
				))}
			</TableRow>
			{hasOptions && (
				<TableRow>
					<TableCell colSpan={row.getVisibleCells().length} className="p-0">
						<InspectionFieldOptions row={row} />
					</TableCell>
				</TableRow>
			)}
		</div>
	);
};
