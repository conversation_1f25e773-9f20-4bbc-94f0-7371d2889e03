import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { IFormFindByIdDto } from "@/modules/inspection/types/forms/dtos/find-by-id.dto";
import { createGetRequest } from "@/shared/lib/requests";
import { useQuery } from "@tanstack/react-query";

export const useFormFindById = (formId: string, enabled: boolean) => {
	const { data, isLoading, isFetched } = useQuery({
		queryKey: ["form-by-id", formId],
		queryFn: () => createGetRequest<IFormFindByIdDto>(INSPECTION_FORM_ENDPOINTS.FIND_BY_ID(formId)),
		enabled,
	});

	return {
		data: data?.success ? data.data : null,
		isLoading,
		hasError: !data?.success && isFetched,
		error: !data?.success ? data?.data.message : undefined,
	};
};
