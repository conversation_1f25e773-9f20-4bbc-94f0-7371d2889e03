import { updateFieldMeasureAtom } from "@/modules/inspection/atoms/forms/fields/field-values.atom";
import useFindAllMeasures from "@/modules/inspection/hooks/measures/list/find-all.hook";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { GenericSearchSelect } from "@/shared/components/custom/generic-search-select";
import { Row } from "@tanstack/react-table";
import { useSetAtom } from "jotai";

export const InspectionFormMeasureRow = ({ row }: { row: Row<ICreateFieldForm> }) => {
	const { tempId, measure } = row.original;
	const updateField = useSetAtom(updateFieldMeasureAtom);

	return (
		<GenericSearchSelect
			value={{ id: measure.id ?? 0, name: measure.name }}
			useDataHook={useFindAllMeasures}
			onChange={value => updateField({ tempId, measure: value })}
			placeholder="Selecione..."
			searchPlaceholder="Buscar medida..."
			loadingText="Carregando..."
			emptyText="Nenhuma medida encontrada."
			width="w-full"
		/>
	);
};
