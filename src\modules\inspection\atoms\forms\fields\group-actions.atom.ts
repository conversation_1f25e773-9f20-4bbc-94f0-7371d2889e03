import { CREATE_DEFAULT_FIELD } from "@/modules/inspection/constants/form/default-field-value";
import { DEFAULT_FIELD_GROUP, DEFAULT_FIELD_GROUP_EMPTY } from "@/modules/inspection/constants/form/default-group-value";
import { IFieldGroup } from "@/modules/inspection/types/forms/fields-table/fields-group.type";
import { arrayMove } from "@dnd-kit/sortable";
import { atom } from "jotai";
import { v4 as uuidv4 } from "uuid";
import { fieldsGroupsAtom } from "./group.atom";

export const addFieldGroupAtom = atom(null, (get, set) => {
	const groups = get(fieldsGroupsAtom);
	const newGroup: IFieldGroup = {
		...DEFAULT_FIELD_GROUP,
		tempId: uuidv4(),
	};
	set(fieldsGroupsAtom, [...groups, newGroup]);
});

export const removeFieldGroupAtom = atom(null, (get, set, groupId: string) => {
	const groups = get(fieldsGroupsAtom);
	set(
		fieldsGroupsAtom,
		groups.filter(group => group.tempId !== groupId),
	);
});

export const updateFieldGroupAtom = atom(null, (get, set, updatedGroup: IFieldGroup) => {
	const groups = get(fieldsGroupsAtom);
	set(
		fieldsGroupsAtom,
		groups.map(group => (group.tempId === updatedGroup.tempId ? updatedGroup : group)),
	);
});

export const updateFieldGroupTitleAtom = atom(null, (get, set, { groupId, newTitle }: { groupId: string; newTitle: string }) => {
	const groups = get(fieldsGroupsAtom);
	set(
		fieldsGroupsAtom,
		groups.map(group => (group.tempId === groupId ? { ...group, groupTitle: newTitle } : group)),
	);
});

export const reorderFieldGroupsAtom = atom(null, (get, set, { fromIndex, toIndex }: { fromIndex: number; toIndex: number }) => {
	const groups = get(fieldsGroupsAtom);
	const updateFields = arrayMove(groups, fromIndex, toIndex);
	let sequenceCounter = 1;
	const groupsWithUpdatedSequence = updateFields.map(group => {
		const updatedItems = group.items.map(item => {
			const updatedItem = { ...item, sequence: sequenceCounter };
			sequenceCounter++;
			return updatedItem;
		});

		return { ...group, items: updatedItems };
	});

	set(fieldsGroupsAtom, groupsWithUpdatedSequence);
});

export const findFieldGroupByIdAtom = atom(null, (get, set, groupId: string) => {
	const groups = get(fieldsGroupsAtom);
	return groups.find(group => group.tempId === groupId);
});

export const getTotalItemsCountAtom = atom(get => {
	const groups = get(fieldsGroupsAtom);
	return groups.reduce((total, group) => total + group.items.length, 0);
});

export const generateEmptyGroupWithItems = atom(null, (get, set) => {
	const groups = get(fieldsGroupsAtom);
	const totalItemsCount = get(getTotalItemsCountAtom);
	const newGroup: IFieldGroup = {
		...DEFAULT_FIELD_GROUP_EMPTY,
		tempId: `empty-${uuidv4()}`,
	};
	const newGroupWithItems = { ...newGroup, items: [CREATE_DEFAULT_FIELD(totalItemsCount + 1)] };
	set(fieldsGroupsAtom, [...groups, newGroupWithItems]);
});
