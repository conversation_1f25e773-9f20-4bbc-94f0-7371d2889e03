// import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
// import { Input } from "@/shared/components/shadcn/input";
// import { Row } from "@tanstack/react-table";
// import { useSetAtom } from "jotai";

// export const InspectionFormGroupTitleRow = ({ row }: { row: Row<ICreateFieldForm> }) => {
// 	const { tempId, groupTitle } = row.original;
// 	const updateField = useSetAtom(updateFieldGroupTitleAtom);

// 	return <Input value={groupTitle} onChange={e => updateField({ tempId, groupTitle: e.target.value })} />;
// };
