import { buildQueryParams } from "@/shared/lib/utils/url-query-params";
import { IFindAllFieldsParamns } from "../hooks/fields/list/find-all.hook";
import { IFindAllInspectionFormParams } from "../hooks/form/list/find-all.hook";
import { IFindAllMeasureaParamns } from "../hooks/measures/list/find-all.hook";

export const INSPECTION_FORM_ENDPOINTS = {
	CREATE: "/inspection/form",
	FIND_ALL: (params: IFindAllInspectionFormParams) =>
		buildQueryParams("/inspection/form", {
			...params,
		}),
	UPDATE: (id: string) => `/inspection/form/${id}`, // put
	DELETE: (id: string) => `/inspection/form/${id}`, // delete
	CLONE: (id: string) => `/inspection/form/${id}/clone`, // post
	FIND_BY_ID: (id: string) => `/inspection/form/${id}`, // get
};

export const MEASURES_ENDPOINTS = {
	FIND_ALL: (params: IFindAllMeasureaParamns) => buildQueryParams("/inspection/measure", { ...params }),
	CREATE: "/inspection/measure",
	DELETE: (id: string) => `/inspection/measure/${id}`,
};

export const FIELDS_ENDPOINTS = {
	CREATE: "/inspection/fields",
	FIND_ALL: (params: IFindAllFieldsParamns) => buildQueryParams("/inspection/fields", { ...params }),
	DELETE: (id: string) => `/inspection/fields/${id}`,
};
