import { v4 as uuidv4 } from "uuid";
import { IFormFindByIdDto } from "../../types/forms/dtos/find-by-id.dto";
import { IFieldGroup } from "../../types/forms/fields-table/fields-group.type";
import { ICreateForm } from "../../validators/form/create";

export class InspectionFormFindByIdToFormMapper {
	static main({ title, text, nomenclature, developer, approver }: IFormFindByIdDto): ICreateForm {
		return {
			title,
			text,
			nomenclature,
			developer: developer
				? {
						id: developer.id.toString(),
						name: developer.name,
					}
				: undefined,
			approver: approver
				? {
						id: approver.id.toString(),
						name: approver.name,
					}
				: undefined,
		};
	}

	static fields({ fields }: IFormFindByIdDto): IFieldGroup[] {
		const mappedFields = fields.map(field => ({
			sequence: field.sequence,
			tempId: uuidv4(),
			field: {
				id: field.field?.id,
				name: field.field?.name,
			},
			nickname: field.nickname,
			required: field.required,
			typeId: field.fieldType?.id,
			options: field.options?.map(option => ({
				id: option.id,
				option: option.option,
				tempId: uuidv4(),
				sequence: option.sequence,
			})),
			measure: {
				id: field.measure?.id,
				name: field.measure?.name,
			},
			biFilter: field.biFilter,
			group: field.group,
			groupTitle: field.groupTitle,
		}));

		const groupedFields: Record<string | number, IFieldGroup> = {};

		mappedFields.forEach(field => {
			const key = field.group ?? `empty-${field.tempId}`;
			if (!groupedFields[key]) {
				groupedFields[key] = {
					group: field.group,
					groupTitle: field.groupTitle,
					items: [],
					tempId: field.group ? uuidv4() : `empty-${uuidv4()}`,
				};
			}
			groupedFields[key].items.push(field);
		});

		return Object.values(groupedFields).sort((a, b) => {
			const groupA = a.group ?? Number.MAX_SAFE_INTEGER;
			const groupB = b.group ?? Number.MAX_SAFE_INTEGER;
			return Number(groupA) - Number(groupB);
		});
	}
}
