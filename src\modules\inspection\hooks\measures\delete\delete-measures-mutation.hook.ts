import { toast } from "@/core/toast";
import { MEASURES_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createDeleteRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useDeleteMeasuresMutation() {
	const queryClient = useQueryClient();

	const deleteMeasures = useMutation({
		mutationKey: ["delete-measures"],
		mutationFn: async (id: string) => {
			const res = await createDeleteRequest<ApiResponse<IMessageGlobalReturn>>(MEASURES_ENDPOINTS.DELETE(id));
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["measures"],
				exact: false,
			});
		},
	});

	return {
		deleteMeasures: (id: string) =>
			toast.promise(deleteMeasures.mutateAsync(id), {
				loading: "Excluindo medida...",
				success: "Medida excluída com sucesso!",
				error: "Erro ao excluir medida.",
			}),
	};
}
