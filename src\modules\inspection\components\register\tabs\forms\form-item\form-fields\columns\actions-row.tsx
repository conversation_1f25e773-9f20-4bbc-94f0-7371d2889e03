import { removeItem<PERSON>rom<PERSON><PERSON><PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields/field-actions.atom";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Button } from "@/shared/components/shadcn/button";
import { Row } from "@tanstack/react-table";
import { useSet<PERSON>tom } from "jotai";
import { Trash2 } from "lucide-react";

export const InspectionFormActionsRow = ({ row }: { row: Row<ICreateFieldForm> }) => {
	const removeField = useSetAtom(removeItemFromGroupAtom);

	const handleRemove = (e: React.MouseEvent) => {
		e.stopPropagation();
		removeField({
			itemId: row.original.tempId,
		});
	};

	return (
		<div className="flex w-full items-center justify-center">
			<Button
				variant="ghost"
				size="sm"
				onClick={handleRemove}
				className="text-destructive hover:text-destructive flex h-8 w-8 items-center justify-center p-0"
			>
				<Trash2 className="h-4 w-4" />
				<span className="sr-only">Excluir campo</span>
			</Button>
		</div>
	);
};
